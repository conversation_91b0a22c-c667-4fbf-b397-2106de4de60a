from odoo import models, fields, api
import logging

_logger = logging.getLogger(__name__)

class StockQuant(models.Model):
    _inherit = 'stock.quant'

    # 添加计算字段用于直接访问位置和批次名称
    location_name = fields.Char(
        string="Location Name",
        compute='_compute_display_names',
        store=False
    )

    lot_name = fields.Char(
        string="Lot Name",
        compute='_compute_display_names',
        store=False
    )

    @api.depends('location_id', 'lot_id')
    def _compute_display_names(self):
        """计算位置和批次名称"""
        for quant in self:
            quant.location_name = quant.location_id.name if quant.location_id else ''
            quant.lot_name = quant.lot_id.name if quant.lot_id else ''
    
    def name_get(self):
        """自定义 quant 的显示名称，格式：location_name - lot_name"""
        result = []
        for quant in self:
            _logger.info(f"name_get called for quant ID {quant.id}")
            
            # 直接访问字段而不是依赖计算字段
            location_name = quant.location_id.name if quant.location_id else None
            lot_name = quant.lot_id.name if quant.lot_id else None
            
            _logger.info(f"Location name: {location_name}, Lot name: {lot_name}")
            
            # 构建显示名称
            display_parts = []
            if location_name:
                display_parts.append(location_name)
            if lot_name:
                display_parts.append(lot_name)

            # 组合显示名称
            name = " - ".join(filter(None, display_parts)) if display_parts else str(quant.id)
            _logger.info(f"Final display name: {name}")
            result.append((quant.id, name))

        return result

    @api.model
    def _name_search(self, name, args=None, operator='ilike', limit=100, name_get_uid=None):
        """扩展搜索功能，支持按位置名称、批次名称等搜索"""
        args = args or []
        
        if name:
            # 支持按位置名称搜索
            location_domain = [('location_id.name', operator, name)]
            # 支持按批次名称搜索
            lot_domain = [('lot_id.name', operator, name)]
            # 组合搜索条件
            search_domain = ['|'] + location_domain + ['|'] + lot_domain + [('id', operator, name)]
            args = search_domain + args
        
        return super(StockQuant, self)._name_search(name, args, operator, limit, name_get_uid)